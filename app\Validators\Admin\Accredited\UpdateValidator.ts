import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class UpdateValidator {
	constructor(protected ctx: HttpContextContract) { }

	public schema = schema.create({
		email: schema.string.optional([rules.trim()]),
		password: schema.string.optional([rules.trim()]),
		name: schema.string.optional([rules.trim()]),
		fantasyName: schema.string.optional([rules.trim()]),
		legal_document_number: schema.string.optional(),
		gender: schema.enum.optional(['masculine', 'feminine'] as const),
		birth_date: schema.date.optional(),
		ddd_phone: schema.number.optional([rules.trim()]),
		phone: schema.number.optional([rules.trim()]),
		ddd_cell: schema.number.optional([rules.trim()]),
		cell: schema.number.optional([rules.trim()]),
		zip_code: schema.string.optional([rules.trim()]),
		street: schema.string.optional([rules.trim()]),
		number: schema.string.optional([rules.trim()]),
		complement: schema.string.optional([rules.trim()]),
		neighborhood: schema.string.optional([rules.trim()]),
		city: schema.string.optional([rules.trim()]),
		state: schema.string.optional([rules.trim()]),
		adviceRegister: schema.string.optional([rules.trim()]),
		paymentMethods: schema.string.optional([rules.trim()]),
		queryValue: schema.number.optional([rules.trim()]),
		accreditedValue: schema.number.optional([rules.trim()]),
		type: schema.enum.optional(['doctor', 'clinic', 'hospital', 'lab'] as const),
		typeOfCare: schema.enum.optional(['in_person', 'video_call', 'both'] as const),
		status: schema.enum.optional(['active', 'inactive', 'punctual'] as const),
		avatarSecureId: schema.string.optional([
			rules.trim(),
			rules.exists({ table: 'uploads', column: 'secure_id' }),
		]),
		groupSecureId: schema.string.optional([
			rules.trim(),
			rules.exists({ table: 'groups', column: 'secure_id' }),
		]),
		specialtiesSecureIds: schema.array
			.optional()
			.members(
				schema.string([rules.trim(), rules.exists({ table: 'specialties', column: 'secure_id' })])
			),
		examsSecureIds: schema.array
			.optional()
			.members(
				schema.string([rules.trim(), rules.exists({ table: 'exams', column: 'secure_id' })])
			),
		showAccreditedInApp: schema.boolean.optional()
	})

	private getFields = (field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`fields.${field}`)

		const isI18N = !fieldI18N.match(/translation/)

		if (isI18N && fieldI18N) {
			return fieldI18N
		} else {
			return field
		}
	}

	private getValidator = (rule, field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`validator.shared.${field}.${rule}`)
		const isI18N = fieldI18N.match(/translation/)

		if (isI18N === null && fieldI18N) {
			return `validator.shared.${field}.${rule}`
		} else {
			return `validator.shared.${rule}`
		}
	}

	public messages: CustomMessages = {
		'*': (field, rule, arrayExpressionPointer, options) => {
			try {
				return this.ctx.i18n.formatMessage(this.getValidator(rule, field), {
					field: this.getFields(field),
				})
			} catch (_) {
				return this.ctx.i18n.validatorMessages()['*'](field, rule, arrayExpressionPointer, options)
			}
		},
		'email.unique': 'Já existe um usuário associado a esse email.',
	}
}
