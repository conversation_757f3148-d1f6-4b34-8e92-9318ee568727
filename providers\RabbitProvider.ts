import type { ApplicationContract } from '@ioc:Adonis/Core/Application'
import Env from '@ioc:Adonis/Core/Env'
import amqplib, { type ConsumeMessage } from 'amqplib'
import Logger from '@ioc:Adonis/Core/Logger'
import ProcessPatientImportResponse from 'App/Services/RabbitQueue/ProcessPatientImportResponse'

export default class RabbitProvider {
	constructor(protected app: ApplicationContract) { }

	public register() {
		this.app.container.singleton('Rabbit', async () => {
			try {
				const conn = await amqplib.connect(Env.get('CLOUD_AMQP_URL'))

				// Add connection error handlers to prevent crashes
				conn.on('error', (err: Error) => {
					Logger.error('RabbitMQ connection error:', err)
				})

				conn.on('close', () => {
					Logger.warn('RabbitMQ connection closed')
				})

				return conn
			} catch (error) {
				Logger.error('Failed to connect to RabbitMQ:', error)
				throw error
			}
		})
	}

	public async boot() {
		this.app.container.singleton('RabbitChannel', async () => {
			try {
				const conn = await this.app.container.resolveBinding('Rabbit')
				const channel = await conn.createChannel()

				// Add channel error handlers to prevent crashes
				channel.on('error', (err: Error) => {
					Logger.error('RabbitMQ channel error:', err)
				})

				channel.on('close', () => {
					Logger.warn('RabbitMQ channel closed')
				})

				return channel
			} catch (error) {
				Logger.error('Failed to create RabbitMQ channel:', error)
				throw error
			}
		})
	}

	public async ready() {
		if (this.app.environment !== 'web') {
			return
		}

		try {
			const channel = await this.app.container.resolveBinding('RabbitChannel')
			const queuePatientImport = 'patient_import_queue'

			// Import patients
			await channel.assertQueue(queuePatientImport, {
				durable: true
			})
			channel.prefetch(1)

			await channel.consume(queuePatientImport, async (msg: ConsumeMessage | null) => {
				// CRITICAL FIX: Add null/undefined checks for message
				if (!msg) {
					Logger.warn('Received null message from RabbitMQ queue. This can happen when the consumer is cancelled or during connection issues.')
					return
				}

				// Additional safety checks for message properties
				if (!msg.content) {
					Logger.error('Received message with null/undefined content:', {
						fields: msg.fields,
						properties: msg.properties
					})
					// Acknowledge the malformed message to prevent it from being redelivered
					channel.ack(msg)
					return
				}

				try {
					// Parse the job ID with error handling
					let jobId: number
					try {
						const parsedContent = JSON.parse(msg.content.toString())
						jobId = parsedContent

						// Validate that jobId is a valid number
						if (typeof jobId !== 'number' || isNaN(jobId) || jobId <= 0) {
							throw new Error(`Invalid jobId: ${jobId}. Expected a positive number.`)
						}
					} catch (parseError) {
						Logger.error('Failed to parse message content as JSON:', {
							error: parseError.message,
							content: msg.content.toString(),
							contentLength: msg.content.length
						})
						// Acknowledge the malformed message to prevent infinite redelivery
						channel.ack(msg)
						return
					}

					Logger.info(`Processing patient import job: ${jobId}`)

					// Process the job with comprehensive error handling
					await ProcessPatientImportResponse(jobId)

					// Only acknowledge after successful processing
					channel.ack(msg)
					Logger.info(`Successfully processed and acknowledged job: ${jobId}`)

				} catch (processingError) {
					Logger.error('Error processing patient import job:', {
						error: processingError.message,
						stack: processingError.stack,
						messageContent: msg.content?.toString(),
						messageFields: msg.fields,
						messageProperties: msg.properties
					})

					// Acknowledge the message to prevent infinite redelivery of failed jobs
					// In production, you might want to implement a dead letter queue instead
					channel.ack(msg)
					Logger.warn('Acknowledged failed message to prevent redelivery loop')
				}
			})

			Logger.info(`RabbitMQ consumer started for queue: ${queuePatientImport}`)

		} catch (error) {
			Logger.error('Failed to setup RabbitMQ consumer:', error)
			// Don't throw here to prevent application crash during startup
		}
	}

	public async shutdown() {
		try {
			const conn = await this.app.container.resolveBinding('Rabbit')
			const channel = await this.app.container.resolveBinding('RabbitChannel')

			if (channel) {
				await channel.close()
				Logger.info('RabbitMQ channel closed successfully')
			}

			if (conn) {
				await conn.close()
				Logger.info('RabbitMQ connection closed successfully')
			}
		} catch (error) {
			Logger.error('Error during RabbitMQ shutdown:', error)
			// Don't throw to allow graceful shutdown to continue
		}
	}
}
