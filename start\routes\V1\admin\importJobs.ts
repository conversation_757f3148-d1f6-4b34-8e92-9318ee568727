import Route from '@ioc:Adonis/Core/Route'
import { isPermissions } from 'App/Contants/Permissions'
import { isRoles } from 'App/Contants/Roles'

Route.group(() => {
  Route.get('import-jobs/:jobId/status', 'V1/Admin/ImportJob/MainController.status').middleware(
    ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_create'])]
  )

  Route.get('/import-jobs/summary', 'V1/Admin/ImportJob/MainController.summary').middleware(
    ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_create'])]
  )

  Route.get('/import-jobs/:jobId/download-errors', 'V1/Admin/ImportJob/MainController.downloadErrors').middleware(
    ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_create'])]
  )

  Route.post('/import-jobs/:jobId/mark-as-read', 'V1/Admin/ImportJob/MainController.markAsRead').middleware(
    ['auth', `${isRoles(['MASTER', 'ADMIN'])}`, isPermissions(['partners_create'])]
  )
})
