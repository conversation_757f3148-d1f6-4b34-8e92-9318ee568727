import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator'
import ImportJob from 'App/Models/ImportJob'
import User from 'App/Models/User'
import Event from '@ioc:Adonis/Core/Event'
import Application from '@ioc:Adonis/Core/Application'

export default class PartnerPatientsController {
	public async index({ response, params, request }: HttpContextContract) {
		const { secure_id } = params
		const { page = 1, limit = 20, search, status } = request.only(['page', 'limit', 'search', 'status'])

		const patients = await User.query()
			.whereHas('partners', builder => {
				builder.where('secure_id', secure_id)
				builder.andWhereHas('roles', (builder) => {
					builder.whereIn('slug', ['patient'])
				})
			})
			// .andWhere('is_active', 1)
			.andWhere((builder) => {
				if (status === 'active') {
					builder.andWhere('users.is_active', true)
				} else if (status === 'inactive') {
					builder.andWhere('users.is_active', false)
				}

				if (search) {
					builder.whereILike('email', `%${search}%`)
					builder.orWhereHas('userInfo', builder => {
						builder.whereILike('name', `%${search}%`)
					})
				}
			})
			.preload('userInfo', builder => {
				builder.select('name', 'legal_document_number', 'ddd_cell', 'cell', 'birth_date')
			})
			.preload('parent')
			.join('user_infos', 'user_infos.user_id', 'users.id')
			// .orderBy('user_infos.name', 'asc')
			.select('users.*')
			.paginate(page, limit)

		return response.ok(patients)
	}

	// public async store({ response, request, params, auth }: HttpContextContract) {
	// 	const { secure_id } = params

	// 	const { users } = await request.validate(StoreValidator)
	// 	const holderUsers = users.filter(user => user.holderLegalDocumentNumber === undefined)
	// 	const dependentUsers = users.filter(user => user.holderLegalDocumentNumber !== undefined)

	// 	const userLogged = auth.user!

	// 	const partner = await User.query()
	// 		.where('secure_id', secure_id)
	// 		.andWhereHas('roles', (builder) => {
	// 			builder.whereIn('slug', ['partner'])
	// 		})
	// 		.first() // Removido o preload('patients')

	// 	if (!partner) {
	// 		return response.notFound({
	// 			type: 'warning',
	// 			message: 'Parceiro não encontrado',
	// 		})
	// 	}

	// 	const newParterPatients: User[] = []
	// 	const newPartnerPatientDependent: User[] = []

	// 	await Database.transaction(async trx => {
	// 		// NOTE: COMENTADO PROVISORIAMENTE A PEDIDO DO CLIENTE
	// 		// Puxa todos os pacientes do parceiro para inativar
	// 		// const oldPatientsToInactivate = await User.query()
	// 		// 	.select('id', 'secure_id')
	// 		// 	.whereHas('partners', (hasBuilder) => {
	// 		// 		hasBuilder.where('partner_id', partner.$attributes.id)
	// 		// 	}).useTransaction(trx)

	// 		// if (oldPatientsToInactivate.length > 0) {
	// 		// 	for await (const patientToInactivate of oldPatientsToInactivate) {
	// 		// 		const patientDependentsToInactive = await User.query()
	// 		// 			.where('parent_id', patientToInactivate.id)
	// 		// 			.useTransaction(trx)

	// 		// 		if (patientDependentsToInactive.length > 0) {
	// 		// 			for await (const dependentToInactivate of patientDependentsToInactive) {
	// 		// 				await User.query()
	// 		// 					.where('id', dependentToInactivate.id)
	// 		// 					.update({ is_active: false })
	// 		// 					.useTransaction(trx)

	// 		// 				await dependentToInactivate.save()
	// 		// 			}
	// 		// 		}

	// 		// 		await User.query()
	// 		// 			.where('id', patientToInactivate.id)
	// 		// 			.update({ is_active: false })
	// 		// 			.useTransaction(trx)

	// 		// 		await patientToInactivate.save();
	// 		// 	}
	// 		// }

	// 		// Faz o cadastro de todos os pacientes titulares
	// 		for await (const currentUser of holderUsers) {
	// 			const { email, password, ...dataUserInfo } = currentUser

	// 			const patient = await User.query({ client: trx })
	// 				.where('email', email)
	// 				.andWhere('type', 'patient')
	// 				.preload('userInfo')
	// 				.first()

	// 			if (patient) {
	// 				patient.merge({
	// 					password: password ? password : dataUserInfo.legalDocumentNumber.replace(/[^0-9]/g, ''),
	// 					is_active: true,
	// 				})
	// 				await patient.useTransaction(trx).save()

	// 				patient.userInfo.merge({ ...dataUserInfo })
	// 				await patient.userInfo.useTransaction(trx).save()

	// 				newParterPatients.push(patient)
	// 			} else {
	// 				const newPatient = new User()
	// 				newPatient.merge({
	// 					email,
	// 					password: password ? password : dataUserInfo.legalDocumentNumber.replace(/[^0-9]/g, ''),
	// 					is_active: true,
	// 				})
	// 				await newPatient.useTransaction(trx).save()

	// 				const newPatientInfo = new UserInfo()
	// 				newPatientInfo.merge({
	// 					typeDocument: 'cpf',
	// 					userId: newPatient.id,
	// 					...dataUserInfo,
	// 				})
	// 				await newPatientInfo.useTransaction(trx).save()

	// 				newPatient.$setRelated('userInfo', newPatientInfo);

	// 				const rolesSearch = await Role.query().where('name', 'PATIENT')
	// 				await newPatient
	// 					.useTransaction(trx)
	// 					.related('roles')
	// 					.sync(rolesSearch.map((role) => role.id))

	// 				newParterPatients.push(newPatient)
	// 			}
	// 		}

	// 		// Faz o cadastro de todos os pacientes dependentes
	// 		for await (const dependent of dependentUsers) {
	// 			const parentDocument = dependent.holderLegalDocumentNumber;
	// 			const { email, password, ...dependentUserData } = dependent;
	// 			delete dependentUserData.holderLegalDocumentNumber;

	// 			// ISSO RESOLVE O DEADLOCK E O TRAVAMENTO.
	// 			const dependentParentData = newParterPatients.find(
	// 				(p) => p.userInfo.legalDocumentNumber === parentDocument
	// 			);

	// 			const patientDependentFromDB = await User.query({ client: trx })
	// 				.andWhere('type', 'dependent')
	// 				.andWhere((andWhereBuilder) => {
	// 					andWhereBuilder.where('email', email)
	// 					andWhereBuilder.orWhereHas('userInfo', (userInfoBuilder) => {
	// 						userInfoBuilder.where('legal_document_number', dependentUserData.legalDocumentNumber)
	// 					})
	// 				})
	// 				.preload('userInfo')
	// 				.first()

	// 			if (patientDependentFromDB) {
	// 				patientDependentFromDB.merge({
	// 					password: password ? password : dependentUserData.legalDocumentNumber.replace(/[^0-9]/g, ''),
	// 					is_active: true,
	// 					parentId: dependentParentData?.id, // Usa o ID do pai encontrado em memória
	// 					type: 'dependent'
	// 				})
	// 				await patientDependentFromDB.useTransaction(trx).save()

	// 				patientDependentFromDB.userInfo.merge({ ...dependentUserData });
	// 				await patientDependentFromDB.userInfo.useTransaction(trx).save();

	// 				newPartnerPatientDependent.push(patientDependentFromDB)
	// 			} else {
	// 				const newDependent = new User()
	// 				newDependent.merge({
	// 					email,
	// 					password: password ? password : dependentUserData.legalDocumentNumber.replace(/[^0-9]/g, ''),
	// 					is_active: true,
	// 					parentId: dependentParentData?.id, // Usa o ID do pai encontrado em memória
	// 					type: 'dependent'
	// 				});
	// 				await newDependent.useTransaction(trx).save();

	// 				const newDependentInfo = new UserInfo();
	// 				newDependentInfo.merge({
	// 					typeDocument: 'cpf',
	// 					userId: newDependent.id,
	// 					...dependentUserData,
	// 				});
	// 				await newDependentInfo.useTransaction(trx).save();

	// 				const rolesSearch = await Role.query().where('name', 'PATIENT');
	// 				await newDependent
	// 					.useTransaction(trx)
	// 					.related('roles')
	// 					.sync(rolesSearch.map((role) => role.id));
	// 				newPartnerPatientDependent.push(newDependent);
	// 			}
	// 		}


	// 		// NOTE: COMENTADO POIS ESTAVA CAUSANDO PROBLEMAS QUANDO TINHA MUITOS PACIENTES (NÃO IRÀ FUNCIONAR SEM O PRELOAD)
	// 		// const uniqueSecureIds = new Set(partner.patients.map(patient => patient.secureId))
	// 		// const filteredNewParterPatients = newParterPatients.filter(patient => !uniqueSecureIds.has(patient.secureId))
	// 		// const patientsSecureIds = [...partner.patients.map(patient => patient.secureId), ...filteredNewParterPatients.map(patient => patient.secureId)]
	// 		// await ActionLogChanges.saveLogUserChanges({
	// 		// 	userChange: partner,
	// 		// 	userChangedData: {
	// 		// 		patientsSecureIds,
	// 		// 	},
	// 		// 	userLogged,
	// 		// 	trx
	// 		// })

	// 		const allPatientIdsToAttach = newParterPatients.map(p => p.id);
	// 		if (allPatientIdsToAttach.length > 0) {
	// 			await partner.useTransaction(trx).related('patients').attach(allPatientIdsToAttach);
	// 		}
	// 	})

	// 	return response.ok({
	// 		type: 'success',
	// 		message: 'Médico adicionado com sucesso!',
	// 	})
	// }

	public async store({ request, response, auth, params }: HttpContextContract) {
		const importValidator = schema.create({
			file: schema.file({ size: '50mb', extnames: ['xlsx'] }),
		})
		const { secure_id } = params

		const { file } = await request.validate({ schema: importValidator })

		const partner = await User.findByOrFail('secure_id', secure_id)

		const localFileName = `${new Date().getTime()}-${file.clientName}`
		await file.move(Application.tmpPath('uploads'), { name: localFileName })
		// const tempFilePath = Application.tmpPath(`uploads/${localFileName}`)
		// await file.move(tempFilePath)

		const job = await ImportJob.create({
			partnerId: partner.id,
			userId: auth.user!.id,
			originalFilename: file.clientName,
			storedFilePath: file.filePath!,
			status: 'pending',
		})

		// const payload = {
		// 	filePath: localFileName,
		// 	partnerId: params.secure_id,
		// 	userId: auth.user!.id,
		// }

		Event.emit('new:patientImport', { jobId: job.id })

		return response.ok({
			jobId: job.id,
			message: 'Sua importação foi recebida e está sendo processada.',
		})
	}

	public async destroy({ response, params }: HttpContextContract) {
		// const userLogged = auth.user!
		const { secure_id, id } = params

		const partner = await User.query()
			.where('secure_id', secure_id)
			.andWhereHas('roles', (builder) => {
				builder.whereIn('slug', ['partner'])
			})
			// .preload('patients') // NOTE: COMENTADO POIS ESTAVA CAUSANDO PROBLEMAS QUANDO TINHA MUITOS PACIENTES
			.first()

		if (!partner) {
			return response.notFound({
				type: 'warning',
				message: 'Parceiro não encontrado',
			})
		}

		const patient = await User.query()
			.where('secure_id', id)
			.andWhere('type', 'patient')
			.first()

		if (!patient) {
			return response.notFound({
				type: 'warning',
				message: 'Paciente não encontrado',
			})
		}

		const existingRelation = await partner
			.related('patients')
			.query()
			.where('user_id', patient.id)
			.first()

		if (!existingRelation) {
			return response.badRequest({
				type: 'warning',
				message: 'Paciente não associado à parceiro',
			})
		}

		// await Database.transaction(async trx => {
		// await ActionLogChanges.saveLogUserChanges({ // NOTE: COMENTADO POIS ESTAVA CAUSANDO PROBLEMAS QUANDO TINHA MUITOS PACIENTES
		// 	userChange: partner,
		// 	userChangedData: {
		// 		patientsSecureIds: [...partner.patients.filter(patientFilter => patientFilter.secureId !== patient.secureId).map(patientOld => patientOld.secureId)]
		// 	},
		// 	userLogged,
		// 	trx
		// })

		// await partner.related('patients').detach([patient.id])
		// })

		return response.ok({
			type: 'success',
			message: 'Paciente desassociado de parceiro!',
		})
	}
}
