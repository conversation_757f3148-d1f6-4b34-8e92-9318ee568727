import { DateTime } from 'luxon'
import { column, BaseModel, hasOne, HasOne } from '@ioc:Adonis/Lucid/Orm'
import User from './User'

export default class UserInfo extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column({ serializeAs: null })
	public userId: number

	@column()
	public name: string

	@column()
	public fantasyName: string
	
	@column({
		prepare: (value: string) => {
			return value?.replace(/\D/g, '')
		},
	})
	public legalDocumentNumber: string

	@column()
	public typeDocument: 'cpf' | 'cnpj'

	@column()
	public gender: 'masculine' | 'feminine'

	@column.dateTime({
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public birthDate: DateTime

	@column()
	public dddPhone: number

	@column()
	public phone: number

	@column()
	public dddCell: number

	@column()
	public cell: number

	@column()
	public zipCode: string

	@column()
	public street: string

	@column()
	public number: string

	@column()
	public complement: string

	@column()
	public neighborhood: string

	@column()
	public city: string

	@column()
	public state: string

	@column()
	public adviceRegister: string

	@column()
	public paymentMethods: string

	@column()
	public queryValue: number

	@column()
	public accreditedValue: number

	@column()
	public typeOfCare: 'in_person' | 'video_call' | 'both'

	@column()
	public status: 'active' | 'inactive' | 'punctual'

	@column()
	public oneSignalKey: string

	@column()
	public origin: string

	@hasOne(() => User, {
		localKey: 'userId',
		foreignKey: 'id',
	})
	public user: HasOne<typeof User>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime
}
