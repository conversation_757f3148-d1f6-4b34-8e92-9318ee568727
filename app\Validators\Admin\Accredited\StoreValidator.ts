import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class StoreValidator {
	constructor(protected ctx: HttpContextContract) { }

	public schema = schema.create({
		userExists: schema.boolean.optional(),
		email: schema.string([rules.trim()]),
		password: schema.string.optional([rules.trim(), rules.requiredWhen('userExists', '!=', true)]),
		name: schema.string([rules.trim()]),
		fantasyName: schema.string([rules.trim()]),
		legal_document_number: schema.string.optional([rules.trim(), rules.cpfOrCnpj()]),
		gender: schema.enum.optional(['masculine', 'feminine'] as const),
		birth_date: schema.date.optional(),
		ddd_phone: schema.number.optional([rules.trim()]),
		phone: schema.number.optional([rules.trim()]),
		ddd_cell: schema.number.optional([rules.trim()]),
		cell: schema.number.optional([rules.trim()]),
		zip_code: schema.string.optional([rules.trim()]),
		street: schema.string.optional([rules.trim()]),
		number: schema.string.optional([rules.trim()]),
		complement: schema.string.optional([rules.trim()]),
		neighborhood: schema.string.optional([rules.trim()]),
		city: schema.string.optional([rules.trim()]),
		state: schema.string.optional([rules.trim()]),
		adviceRegister: schema.string([rules.trim()]),
		paymentMethods: schema.string([rules.trim()]),
		queryValue: schema.number([rules.trim()]),
		accreditedValue: schema.number([rules.trim()]),
		typeOfCare: schema.enum(['in_person', 'video_call', 'both'] as const),
		type: schema.enum(['doctor', 'clinic', 'hospital', 'lab'] as const),
		status: schema.enum(['active', 'inactive', 'punctual'] as const),
		avatarSecureId: schema.string.optional([
			rules.trim(),
			rules.exists({ table: 'uploads', column: 'secure_id' }),
		]),
		groupSecureId: schema.string.optional([
			rules.trim(),
			rules.exists({ table: 'groups', column: 'secure_id' }),
		]),
		specialtiesSecureIds: schema.array
			.optional()
			.members(
				schema.string([rules.trim(), rules.exists({ table: 'specialties', column: 'secure_id' })])
			),
		examsSecureIds: schema.array
			.optional()
			.members(
				schema.string([rules.trim(), rules.exists({ table: 'exams', column: 'secure_id' })])
			),
	})

	public async validateDocumentNumber() {
		const legalDocumentNumber = this.ctx.request.input('legal_document_number')
		if (legalDocumentNumber) {
			const cleanedValue = legalDocumentNumber.replace(/\D/g, '') // Remove caracteres não numéricos

			// Validação de CPF ou CNPJ
			if (cleanedValue.length === 11) {
				// Aplica validação de CPF
				const isValidCPF = rules.cpf()
				if (!isValidCPF) {
					throw new Error('Número de CPF inválido.')
				}
			} else if (cleanedValue.length === 14) {
				// Aplica validação de CNPJ
				const isValidCNPJ = rules.cnpj()
				if (!isValidCNPJ) {
					throw new Error('Número de CNPJ inválido.')
				}
			} else {
				throw new Error('O número do documento deve ter 11 dígitos para CPF ou 14 dígitos para CNPJ.')
			}
		}
	}

	private getFields = (field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`fields.${field}`)

		const isI18N = !fieldI18N.match(/translation/)

		if (isI18N && fieldI18N) {
			return fieldI18N
		} else {
			return field
		}
	}

	private getValidator = (rule, field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`validator.shared.${field}.${rule}`)
		const isI18N = fieldI18N.match(/translation/)

		if (isI18N === null && fieldI18N) {
			return `validator.shared.${field}.${rule}`
		} else {
			return `validator.shared.${rule}`
		}
	}

	public messages: CustomMessages = {
		'*': (field, rule, arrayExpressionPointer, options) => {
			try {
				return this.ctx.i18n.formatMessage(this.getValidator(rule, field), {
					field: this.getFields(field),
				})
			} catch (_) {
				return this.ctx.i18n.validatorMessages()['*'](field, rule, arrayExpressionPointer, options)
			}
		},
		'email.unique': 'Já existe um usuário associoado a esse email.',
	}
}
