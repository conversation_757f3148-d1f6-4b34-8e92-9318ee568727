import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class StoreValidator {
	constructor(protected ctx: HttpContextContract) { }

	public schema = schema.create({
		typeConsult: schema.enum(['in_person', 'video_call', 'exam', 'laboratory'] as const),
		status: schema.enum([
			'in_schedule',
			'waiting_backoffice',
			'waiting_backoffice_network',
			'budget',
			'waiting_patient',
			'approved',
			'canceled',
			'canceled_by_patient',
			'canceled_at_patient_request',
			'canceled_by_backoffice',
			"in_accreditation",
			"no_contact",
			"no_interest",
			"lack_request",
			"info_divergence",
			"financial_condition",
			"no_interest_accreditation",
		] as const),
		specialtyOrExamSecureId: schema.string([
			rules.exists({
				table: this.isExamOrLaboratory(this.ctx.request.body().typeConsult) ? 'exams' : 'specialties',
				column: 'secure_id',
			}),
		]),
		patientSecureId: schema.string([
			rules.exists({
				table: 'users',
				column: 'secure_id',
			}),
		]),
		attendantType: schema.enum(['helloMed', 'doctor', 'clinic', 'lab'] as const),
		accreditedSecureId: schema.string.optional([
			rules.trim(),
			rules.exists({ table: 'users', column: 'secure_id' }),
			// rules.requiredWhen('attendantType', '!=', 'helloMed', ),
		]),
		local: schema.object.optional().members({
			neighborhood: schema.string.optional(),
			city: schema.string.optional(),
			state: schema.string.optional(),
		}),
		dates: schema.array.optional().members(
			schema.object().members({
				date: schema.date(),
				type: schema.enum(['hour', 'period'] as const),
				value: schema.enum.optional(['morning', 'afternoon', 'night'] as const, [
					rules.requiredWhen('type', '=', 'period'),
				]),
			})
		),
		observation: schema.string.optional(),
		queryValueSubsidy: schema.number.optional(),
		queryValuePatient: schema.number.optional(),
	})

	private isExamOrLaboratory(typeConsult: string): boolean {
		return typeConsult === 'exam' || typeConsult === 'laboratory'
	}

	private getFields = (field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`fields.${field}`)

		const isI18N = !fieldI18N.match(/translation/)

		if (isI18N && fieldI18N) {
			return fieldI18N
		} else {
			return field
		}
	}

	private getValidator = (rule, field) => {
		const fieldI18N = this.ctx.i18n.formatMessage(`validator.shared.${field}.${rule}`)
		const isI18N = fieldI18N.match(/translation/)

		if (isI18N === null && fieldI18N) {
			return `validator.shared.${field}.${rule}`
		} else {
			return `validator.shared.${rule}`
		}
	}

	public messages: CustomMessages = {
		'*': (field, rule, arrayExpressionPointer, options) => {
			try {
				return this.ctx.i18n.formatMessage(this.getValidator(rule, field), {
					field: this.getFields(field),
				})
			} catch (_) {
				return this.ctx.i18n.validatorMessages()['*'](field, rule, arrayExpressionPointer, options)
			}
		},
	}
}
