import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'schedules'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enum('type_consult', ['exam', 'in_person', 'video_call', 'laboratory'])
        .alter()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enum('type_consult', ['exam', 'in_person', 'video_call'])
        .alter()
    })
  }
}
