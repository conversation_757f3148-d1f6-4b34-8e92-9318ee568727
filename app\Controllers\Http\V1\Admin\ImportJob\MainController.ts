import ImportJob from 'App/Models/ImportJob'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ExcelJS from 'exceljs'


export default class ImportsController {
  public async status({ params, response }: HttpContextContract) {
    const { jobId } = params;

    const job = await ImportJob.findOrFail(jobId);

    // if (job.userId !== auth.user!.id) {
    //   return response.forbidden();
    // }

    return response.ok({
      status: job.status,
      totalRows: job.totalRows,
      processedRows: job.processedRows,
      errorsReport: job.errorsReport,
    });
  }

  public async summary({ auth, response }: HttpContextContract) {
    const userId = auth.user!.id;

    const processingJobs = await ImportJob.query()
      .where({ userId })
      .whereIn('status', ['pending', 'processing']);

    const recentFinishedJobs = await ImportJob.query()
      .where({ userId, isRead: false }) // <<< Só pega as não lidas
      .whereIn('status', ['completed', 'failed', 'failed_with_errors'])
      .orderBy('updatedAt', 'desc')
      .limit(5); // Pega as 5 mais recentes

    return response.ok({
      processingJobs,
      recentFinishedJobs,
    });
  }

  public async downloadErrors({ params, response }: HttpContextContract) {
    const job = await ImportJob.findOrFail(params.jobId);

    if (!job.errorsReport) {
      return response.notFound({ message: 'Nenhum relatório de erros encontrado para este job.' })
    }

    const errorsReport = JSON.parse(job.errorsReport)

    if (!Array.isArray(errorsReport) || errorsReport.length === 0) {
      return response.notFound({ message: 'O relatório de erros está vazio.' });
    }

    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Erros da Importação')

    worksheet.columns = [
      { header: 'Linha na Planilha Original', key: 'rowNumber', width: 25 },
      { header: 'Erro Encontrado', key: 'error', width: 60 },
      { header: 'Nome', key: 'name', width: 30 },
      { header: 'E-mail', key: 'email', width: 30 },
      { header: 'CPF', key: 'cpf', width: 20 },
      { header: 'CPF do Titular', key: 'holder_cpf', width: 20 },
    ]

    for (const report of errorsReport) {
      worksheet.addRow({
        rowNumber: report.rowNumber,
        error: report.error,
        name: report.data.name,
        email: report.data.email,
        cpf: report.data.legalDocumentNumber,
        holder_cpf: report.data.holderLegalDocumentNumber,
      })
    }

    const fileName = `erros_importacao_${job.id}.xlsx`
    response.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response.header('Content-Disposition', `attachment; filename="${fileName}"`)

    const buffer = await workbook.xlsx.writeBuffer()
    return response.send(buffer)
  }

  public async markAsRead({ params, response }: HttpContextContract) {
    const job = await ImportJob.findOrFail(params.jobId);
    job.isRead = true;
    await job.save();
    return response.ok(job);
  }
}