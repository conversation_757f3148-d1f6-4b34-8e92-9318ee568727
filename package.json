{"name": "hello-med", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production", "start": "node server.js", "test": "node ace test", "lint": "eslint . --ext=.ts", "deploy": "git pull && yarn && node ace migration:run --force && node ace deploy && yarn build && cp .env build/", "deployCapRover": "yarn build --ignore-ts-errors", "format": "prettier --write ."}, "eslintConfig": {"extends": ["plugin:adonis/typescriptApp", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": ["error"]}}, "eslintIgnore": ["build"], "prettier": {"trailingComma": "es5", "semi": false, "singleQuote": true, "useTabs": true, "quoteProps": "consistent", "bracketSpacing": true, "arrowParens": "always", "printWidth": 100}, "devDependencies": {"@adonisjs/assembler": "^5.9.5", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^2.5.1", "@types/amqplib": "^0.10.7", "@types/proxy-addr": "^2.0.0", "@types/source-map-support": "^0.5.6", "@types/uuid": "^9.0.2", "adonis-preset-ts": "^2.1.0", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-adonis": "^2.1.1", "eslint-plugin-prettier": "^4.2.1", "pino-pretty": "^10.0.0", "prettier": "^2.8.8", "typescript": "~4.6", "youch": "^3.2.3", "youch-terminal": "^2.2.0"}, "dependencies": {"@adonisjs/auth": "^8.2.3", "@adonisjs/core": "^5.8.0", "@adonisjs/drive-s3": "^1.3.2", "@adonisjs/i18n": "^1.5.6", "@adonisjs/lucid": "^21.8.0", "@adonisjs/mail": "^8.2.0", "@adonisjs/repl": "^3.1.0", "@adonisjs/view": "^6.2.0", "@onesignal/node-onesignal": "^2.0.1-beta2", "@zenvia/sdk": "^2.4.4", "adonis5-swagger": "^1.4.1", "amqplib": "^0.10.8", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "exceljs": "^4.4.0", "luxon": "^3.3.0", "marked": "^4.3.0", "moment": "^2.30.1", "mysql2": "^3.2.4", "proxy-addr": "^2.0.7", "reflect-metadata": "^0.1.13", "source-map-support": "^0.5.21", "twilio": "^4.18.0", "uuid": "^9.0.0", "xlsx": "^0.18.5"}}