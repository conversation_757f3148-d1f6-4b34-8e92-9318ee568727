import { DateTime } from 'luxon'
import {
	BaseModel,
	beforeCreate,
	beforeSave,
	BelongsTo,
	belongsTo,
	column,
	HasMany,
	hasMany,
	HasOne,
	hasOne,
	manyToMany,
	ManyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import { v4 as uuid } from 'uuid'
import User from './User'
import Specialty from './Specialty'
import Exam from './Exam'
import Appointment from './Appointment'
import ScheduleDatesRequest from './ScheduleDatesRequest'
import Upload from './Upload'
import Observations from './Observations'

export default class Schedule extends BaseModel {
	@column({ isPrimary: true, serializeAs: null })
	public id: number

	@column()
	public secureId: string

	@column({ serializeAs: null })
	public specialtyId: number | null

	@column({ serializeAs: null })
	public examId: number | null

	@column.dateTime({
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
		serializeAs: 'followUpDate'
	})
	public follow_up_date: DateTime

	@column({ serializeAs: null })
	public userId: number

	@column({ serializeAs: null })
	public patientId: number

	@column({ serializeAs: null })
	public userByCanceledId: number | null

	@column()
	public typeConsult: 'in_person' | 'video_call' | 'exam' | 'laboratory'

	@column()
	public currentStatus:
		'open' |
		'closed'

	@column()
	public status:
		'in_schedule' |
		'waiting_backoffice' |
		'waiting_backoffice_network' |
		'budget' |
		'waiting_patient' |
		'approved' |
		'canceled' |
		'canceled_by_patient' |
		'canceled_at_patient_request' |
		'canceled_by_backoffice' |
		"in_accreditation" |
		"no_contact" |
		"no_interest" |
		"lack_request" |
		"info_divergence" |
		"financial_condition" |
		"no_interest_accreditation"

	@column()
	public hasBeenAccredited: boolean

	@column()
	public neighborhood: string

	@column()
	public city: string

	@column()
	public state: string

	@column.dateTime({
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public dateCanceled: DateTime | null

	@column()
	public typeCanceled: 'patient' | 'backoffice'

	@column()
	public motiveCanceled: string

	@belongsTo(() => Specialty)
	public specialty: BelongsTo<typeof Specialty>

	@belongsTo(() => Exam)
	public exam: BelongsTo<typeof Exam>

	@belongsTo(() => User)
	public user: BelongsTo<typeof User>

	@hasOne(() => User, {
		localKey: 'patientId',
		foreignKey: 'id',
	})
	public patient: HasOne<typeof User>

	@belongsTo(() => User, {
		localKey: 'userByCanceledId',
		foreignKey: 'id',
	})
	public userByCanceled: BelongsTo<typeof User>

	@belongsTo(() => Appointment, {
		localKey: 'scheduleId',
		foreignKey: 'id',
	})
	public appointment: BelongsTo<typeof Appointment>

	@hasMany(() => ScheduleDatesRequest)
	public scheduleDatesRequests: HasMany<typeof ScheduleDatesRequest>

	@manyToMany(() => Upload, { ///
		pivotTable: 'schedule_uploads',
		localKey: 'id',
		pivotForeignKey: 'upload_id',
		relatedKey: 'id',
		pivotRelatedForeignKey: 'schedule_id',
	})
	public uploads: ManyToMany<typeof Upload>;

	@hasMany(() => Observations, {
		foreignKey: 'schedule_id',
		localKey: 'id',
	})
	public observations: HasMany<typeof Observations>

	@column.dateTime({
		autoCreate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public createdAt: DateTime

	@column.dateTime({
		autoCreate: true,
		autoUpdate: true,
		serialize: (value: DateTime) => {
			if (value) return value.toFormat('yyyy-MM-dd HH:mm:ss').replace(/\s+/g, 'T')
			return
		},
	})
	public updatedAt: DateTime

	@beforeCreate()
	public static async createUUID(model: Schedule) {
		model.secureId = uuid()
	}

	@beforeSave()
	public static async updateHasBeenAccredited(schedule: Schedule) {
		if (schedule.status === 'in_accreditation') {
			schedule.hasBeenAccredited = true
		}
	}
}
