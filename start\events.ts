import Event from '@ioc:Adonis/Core/Event'
import Application from '@ioc:Adonis/Core/Application'
import Mail from '@ioc:Adonis/Addons/Mail'
import Env from '@ioc:Adonis/Core/Env'
import * as zenvia from '@zenvia/sdk';
import Logger from '@ioc:Adonis/Core/Logger'
import * as OneSignal from '@onesignal/node-onesignal'
import { DateTime } from 'luxon'
import { format, parseISO } from 'date-fns'
import { utcToZonedTime } from 'date-fns-tz'

type TemplateProps = "consultSolicitation" |
	"examSolicitation" |
	"consultConfirmation" |
	"examConfirmation" |
	"consultOptions" |
	"examOptions" |
	"consult3h" |
	"consult24h" |
	"exam3h" |
	"exam24h" |
	"examCancelForPatient" |
	"consultCancelForPatient" |
	"examCancelForPatientSendBackoffice" |
	"consultCancelForPatientSendBackoffice"

type NewSendMailProps = {
	email: string
	template: TemplateProps
	payload: {
		template?: string
		email?: string
		dataTemplate: {
			buttonNewPassword?: string
			password?: string
		}
	}
	subject: string
}

Event.on('new:SendMail', async ({ email, template, subject, payload }: NewSendMailProps) => {
	try {
		await Mail.use('smtp').send((message) => {
			message
				.from(Env.get('MAIL_EMAIL_APPLICATION'))
				.to(email)
				.subject(subject)
				.htmlView(`emails/${template}`, {
					title: subject,
					email,
					payload,
				})
		})
	} catch (err) {
		Logger.error(err)
	}
})

Event.on('new:zenviaSms', async (data: { content: string, phone: string }) => {
	try {
		const client = new zenvia.Client(Env.get('ZENVIA_TOKEN'));
		const sms = client.getChannel('sms');
		const content = new zenvia.TextContent(data.content);
		await sms.sendMessage(Env.get('KEYWORD'), data.phone, content);
	}
	catch (error) {
		Logger.error(error);
	}
})

Event.on('new:oneSignalNotification', async (data: { title: string, content: string, ids: [], date?: DateTime }) => {
	const configuration = OneSignal.createConfiguration({
		userKey: Env.get('ONESIGNAL_USER_KEY'),
		appKey: Env.get('ONESIGNAL_API_KEY'),
	});

	const client = new OneSignal.DefaultApi(configuration)

	const sendNotification = new OneSignal.Notification()
	sendNotification.app_id = Env.get('ONESIGNAL_APP_ID')
	sendNotification.chrome_web_badge = "https://hellomed.com.br/wp-content/uploads/2018/09/logo.png"
	sendNotification.chrome_web_icon = "https://hellomed.com.br/wp-content/uploads/2018/09/logo.png"
	sendNotification.name = data.title
	sendNotification.headings = {
		pt: data.title
	}
	sendNotification.contents = {
		en: data.content,
		pt: data.content
	}

	sendNotification.include_player_ids = data.ids

	if (data.date) {
		const formattedDate = parseISO(data.date!.toString())
		const dateInTimeZone = utcToZonedTime(formattedDate, 'America/Sao_Paulo')
		const formattedDateString = format(dateInTimeZone, 'yyyy-MM-dd HH:mm:ss OOOO')
		sendNotification.send_after = formattedDateString
	}

	try {
		await client.createNotification(sendNotification)
	} catch (error) {
		// Logger.error('Erro ao enviar a notificação OneSignal', error)
	}
})

Event.on('new:oneSignalEmail', async (data: { subject: string, content: string, emails: [], date?: DateTime }) => {
	const configuration = OneSignal.createConfiguration({
		userKey: Env.get('ONESIGNAL_USER_KEY_EMAIL'),
		appKey: Env.get('ONESIGNAL_API_KEY_EMAIL'),
	});

	const client = new OneSignal.DefaultApi(configuration)

	const sendNotification = new OneSignal.Notification()
	sendNotification.app_id = Env.get('ONESIGNAL_APP_ID_EMAIL')
	sendNotification.email_subject = data.subject
	sendNotification.email_body = data.content
	sendNotification.include_email_tokens = data.emails

	if (data.date) {
		const formattedDate = parseISO(data.date!.toString())
		const dateInTimeZone = utcToZonedTime(formattedDate, 'America/Sao_Paulo')
		const formattedDateString = format(dateInTimeZone, 'yyyy-MM-dd HH:mm:ss OOOO')
		sendNotification.send_after = formattedDateString
	}

	try {
		await client.createNotification(sendNotification)
	} catch (error) {
		//Logger.error('Erro ao enviar a notificação OneSignal', error)
	}
})

Event.on('new:patientImport', async ({ jobId }) => {
	const queueName = 'patient_import_queue'

	try {
		const channel = await Application.container.resolveBinding('RabbitChannel')

		await channel.assertQueue(queueName, { durable: true })

		channel.sendToQueue(queueName, Buffer.from(JSON.stringify(jobId)), {
			persistent: true,
		});

		// amqp.connect(Env.get('CLOUD_AMQP_URL'), (error0, connection) => {
		// 	if (error0) {
		// 		throw error0;
		// 	}

		// 	connection.createChannel((error1, channel) => {
		// 		if (error1) {
		// 			throw error1;
		// 		}
		// 		const queue = `patient_import_queue`

		// 		// delete payload.tempFilePath

		// 		channel.sendToQueue(queue, Buffer.from(JSON.stringify(payload)), {
		// 			persistent: true,
		// 			priority: 5
		// 		});
		// 	});
		// 	setTimeout(() => {
		// 		connection.close();
		// 	}, 500);
		// });
	} catch (error) {
		console.error(error)
	}
})
