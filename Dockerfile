# Base image
FROM node:20.15.0
ARG PORT=${PORT}
ARG HOST=${HOST}
ARG NODE_ENV=${NODE_ENV}
ARG APP_KEY=${APP_KEY}
ARG DRIVE_DISK=${DRIVE_DISK}
ARG DB_CONNECTION=${DB_CONNECTION}
ARG MYSQL_HOST=${MYSQL_HOST}
ARG MYSQL_PORT=${MYSQL_PORT}
ARG MYSQL_USER=${MYSQL_USER}
ARG MYSQL_PASSWORD=${MYSQL_PASSWORD}
ARG MYSQL_DB_NAME=${MYSQL_DB_NAME}
ARG CACHE_VIEWS=${CACHE_VIEWS}
ARG S3_KEY=${S3_KEY}
ARG S3_SECRET=${S3_SECRET}
ARG S3_ENDPOINT=${S3_ENDPOINT}
ARG S3_BUCKET=${S3_BUCKET}
ARG S3_REGION=${S3_REGION}
ARG S3_CDN=${S3_CDN}
ARG KEYWORD=${KEYWORD}
ARG ZENVIA_TOKEN=${ZENVIA_TOKEN}
ARG ONESIGNAL_APP_ID=${ONESIGNAL_APP_ID}
ARG ONESIGNAL_API_KEY=${ONESIGNAL_API_KEY}
ARG ONESIGNAL_USER_KEY=${ONESIGNAL_USER_KEY}
ARG ONESIGNAL_API_KEY_EMAIL=${ONESIGNAL_API_KEY_EMAIL}
ARG ONESIGNAL_APP_ID_EMAIL=${ONESIGNAL_APP_ID_EMAIL}
ARG ONESIGNAL_USER_KEY_EMAIL=${ONESIGNAL_USER_KEY_EMAIL}
ARG TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
ARG TWILIO_API_KEY_SID=${TWILIO_API_KEY_SID}
ARG TWILIO_API_KEY_SECRET=${TWILIO_API_KEY_SECRET}
ARG MAIL_EMAIL_APPLICATION=${MAIL_EMAIL_APPLICATION}
ARG SMTP_HOST=${SMTP_HOST}
ARG SMTP_PORT=${SMTP_PORT}
ARG SMTP_USERNAME=${SMTP_USERNAME}
ARG SMTP_PASSWORD=${SMTP_PASSWORD}
ARG CLOUD_AMQP_URL=${CLOUD_AMQP_URL}

# Create app directory
WORKDIR /usr/src/app

# Expose port
EXPOSE 3333:3333

# A wildcard is used to ensure both package.json AND package-lock.json are copied
COPY package.json ./


# Install app dependencies
RUN yarn

# Bundle app source
COPY . .

# Creates a "dist" folder with the production build
RUN yarn deployCapRover

ENV TZ="America/Sao_Paulo"
RUN date

# Start the server using the production build
CMD [ "sh", "-c", "node ace migration:run --force && node ace deploy && node build/server.js" ]
