import { BaseCommand } from '@adonisjs/core/build/standalone'
import { DateTime } from 'luxon'
import xlsx from 'xlsx'
import { v4 as uuid } from 'uuid'
import Appointment from 'App/Models/Appointment'
import User from 'App/Models/User'
import UserInfo from 'App/Models/UserInfo'
import Specialty from 'App/Models/Specialty'
import Schedule from 'App/Models/Schedule'
import Observations from 'App/Models/Observations'
import Role from 'App/Models/Role'

type AppointmentHistory = {
  openingDate: string;
  ownerCPF: string;
  partnerName: string;
  ownerName: string;
  dependentName: string;
  patientPhone: string;
  city: string;
  state: string;
  observation?: string;
  specialty: string;
}

type AppointmentHistoryExcel = {
  data: number;
  id_hellomed: string,
  associacao: string;
  nome_titular: string;
  paciente: string;
  telefone_de_contato: string;
  cidade: string;
  estado: string;
  historico: string;
  especialidade: string;
}

export default class ImportAppointmentsHistory extends BaseCommand {
  public static commandName = 'import:appointments_history'
  public static description = ''

  public static settings = {
    loadApp: true,
    stayAlive: false,
  }

  private getProgressBar(currentPercentage: number) {
    const completed = Math.ceil(currentPercentage / 3)
    const incomplete = Math.ceil((100 - currentPercentage) / 3)
    return `[${new Array(completed).join('=')}${new Array(incomplete).join(' ')}]`
  }

  public async run() {
    try {
      const appointments = xlsx.readFile('PLANILHA_ATENDIMENTO_hellomed.xlsx') // Arquivo da planilha
      // const appointments = xlsx.readFile('TESTE.xlsx') // Arquivo da planilha
      // const sheetName = appointments.SheetNames[2] // Página da tabela
      const sheetName = appointments.SheetNames[0] // Página da tabela
      const worksheet = appointments.Sheets[sheetName];
      const start = 'A1'
      // const end = 'Z10546' // Última linha
      const end = 'Z12247' // Última linha


      const jsonData = xlsx.utils.sheet_to_json(worksheet, {
        range: start + ':' + end,
      });

      const formatData = (data) => {
        return Object.fromEntries(
          Object.entries(data).map(([key, value]) => {
            const formattedKey = key
              .replace(/\s*=\s*/g, '_')
              .replace(/\//g, '')
              .replace(/\s+/g, '_')
              .replace(/[áàâãä]/g, 'a')
              .replace(/[éèêë]/g, 'e')
              .replace(/[íìîï]/g, 'i')
              .replace(/[óòôõö]/g, 'o')
              .replace(/[úùûü]/g, 'u')
              .replace(/[ÁÀÂÃÄ]/g, 'A')
              .replace(/[ÉÈÊË]/g, 'E')
              .replace(/[ÍÌÎÏ]/g, 'I')
              .replace(/[ÓÒÔÕÖ]/g, 'O')
              .replace(/[ÚÙÛÜ]/g, 'U')
              .toLowerCase();

            return [formattedKey, value];
          })
        );
      };

      const formatValue = (value) => {
        return String(value)
          .toLowerCase()
          .replace(/\b\w/g, (char) => char.toUpperCase());
      };

      const formattedData = jsonData.map(formatData) as AppointmentHistoryExcel[]

      function parseAndFormatDate(excelDate: any): string {
        try {
          if (!excelDate) return '1970-01-01';

          if (typeof excelDate === 'number') {
            const jsDate = new Date((excelDate - 25569) * 86400 * 1000);
            return DateTime.fromJSDate(jsDate, { zone: 'utc' }).toFormat('dd/MM/yyyy');
          }
          if (excelDate instanceof Date) {
            return DateTime.fromJSDate(excelDate).toFormat('dd/MM/yyyy');
          }
          if (typeof excelDate === 'string') {
            const parsedDate = DateTime.fromFormat(excelDate, 'dd/MM/yyyy');
            if (parsedDate.isValid) return parsedDate.toFormat('dd/MM/yyyy');
          }
        } catch (error) {
        }
        return '1970-01-01'; // Data inválida se nada funcionar
      }

      const filteredData = formattedData.filter((item) => {
        return Object.values(item).some(value => value && value.toString().trim() !== '');
      });

      const appointmentData: AppointmentHistory[] = filteredData.map((item) => {
        // const excelDateToJSDate = (excelDate) => {
        //   const jsDate = new Date((excelDate - 25569) * 86400 * 1000);
        //   jsDate.setDate(jsDate.getDate() + 1);
        //   return jsDate.toLocaleDateString('pt-BR');
        // };

        const formattedDate = parseAndFormatDate(item.data) || '01/01/1969';
        // const formattedDate = excelDateToJSDate(excelDate);

        let cpf = '000.000.000-00'
        let patientPhone = '00 000000000'
        let specialty = 'Indefinido'

        if (item.id_hellomed) {
          cpf = String(item.id_hellomed).replace(/\s+/g, '')
        }
        if (item.telefone_de_contato) {
          patientPhone = String(item.telefone_de_contato).replace(/\D/g, '')
        }
        if (item.especialidade) {
          specialty = item.especialidade.trim().toLowerCase().replace(/\b\w/g, char => char.toUpperCase());
        }

        return {
          openingDate: formattedDate,
          ownerCPF: cpf,
          partnerName: formatValue(item.associacao),
          ownerName: formatValue(item.nome_titular),
          dependentName: formatValue(item.paciente),
          patientPhone: patientPhone,
          city: formatValue(item.cidade),
          state: item.estado,
          observation: item.historico,
          specialty: specialty
        }
      }).filter(Boolean)

      let excelUser = await User.query().where('email', '<EMAIL>').first()

      if (!excelUser) {
        excelUser = await User.create({
          secureId: uuid(),
          email: '<EMAIL>',
          password: 'default_password',
          type: 'admin',
          is_active: true,
          deleted: false,
        });

        await UserInfo.create({
          userId: excelUser.id,
          name: 'Usuário Excel',
          legalDocumentNumber: '000.000.000-00',
          typeDocument: 'cpf'
        })
      }

      let excelPartner = await User.query().where('email', '<EMAIL>').first()

      if (!excelPartner) {
        excelPartner = await User.create({
          secureId: uuid(),
          email: '<EMAIL>',
          password: 'default_password',
          type: 'doctor',
          is_active: true,
          deleted: false,
        });

        await UserInfo.create({
          userId: excelUser.id,
          name: 'Parceiro Excel',
          legalDocumentNumber: '111.111.111-11',
          typeDocument: 'cpf'
        })
      }

      const patientRole = await Role.query().where('name', 'PATIENT').firstOrFail();
      const totalAppointments = appointmentData.length;

      for (const [index, appointment] of appointmentData.entries()) {
        const progressPercentage = Math.round(((index + 1) / totalAppointments) * 100);
        const progressBar = this.getProgressBar(progressPercentage);

        this.logger.info(`Importando ${index + 1} de ${totalAppointments}: ${progressBar} ${progressPercentage}%`);

        const userInfoOwner = await UserInfo.query().where('legalDocumentNumber', appointment.ownerCPF).first();
        const userInfoDependent = await UserInfo.query().where('name', appointment.dependentName).first();
        const specialties = await Specialty.query().where('name', appointment.specialty).first();

        let specialty;
        let userOwner;
        let userDependent;

        // Obtenha ou crie a especialidade
        if (specialties) {
          specialty = await Specialty.findBy('id', specialties.id);
        } else {
          specialty = await Specialty.create({
            secureId: uuid(),
            thumbId: 1,
            name: appointment.specialty,
            active: true,
          });
        }

        // Processa titular e dependente
        if (appointment.ownerName !== appointment.dependentName) {
          if (userInfoOwner) {
            userOwner = await User.findBy('id', userInfoOwner.userId);
          } else {
            userOwner = await User.create({
              secureId: uuid(),
              email: `${appointment.ownerName.replace(/\s+/g, '').toLowerCase()}@noemail.com`,
              password: 'default_password',
              type: 'patient',
              is_active: true,
              deleted: false,
            });

            // Associa role PATIENT ao titular
            await userOwner.related('roles').attach([patientRole.id]);

            await UserInfo.create({
              userId: userOwner.id,
              name: appointment.ownerName,
              legalDocumentNumber: appointment.ownerCPF,
              typeDocument: 'cpf',
              dddCell: Number(appointment.patientPhone.slice(0, 2)),
              cell: Number(appointment.patientPhone.slice(2, 11)),
              city: appointment.city,
              state: appointment.state,
            });
          }

          if (userInfoDependent) {
            userDependent = await User.findBy('id', userInfoDependent.userId);
          } else {
            userDependent = await User.create({
              secureId: uuid(),
              parentId: userOwner.id,
              email: `${appointment.dependentName.replace(/\s+/g, '').toLowerCase()}@noemail.com`,
              password: 'default_password',
              type: 'dependent',
              is_active: true,
              deleted: false,
            });

            // Associa role PATIENT ao dependente
            await userDependent.related('roles').attach([patientRole.id]);

            await UserInfo.create({
              userId: userDependent.id,
              name: appointment.dependentName,
              city: appointment.city,
              state: appointment.state,
            });
          }

          if (userDependent && specialty) {
            const appointmentDate = DateTime.fromFormat(appointment.openingDate, 'dd/MM/yyyy');
            if (appointmentDate.isValid) {
              const existingAppointment = await Appointment.query()
                .where('patientId', userDependent.id)
                .where('specialtyId', specialty.id)
                .where('date', appointmentDate.toISODate()!)
                .first();

              if (existingAppointment) {
                this.logger.info(`[AVISO] Agendamento já existe para dependente ${userDependent.id} em ${appointment.openingDate}. Pulando.`);
                continue;
              }
            }
          }

          const schedule = await Schedule.create({
            secureId: uuid(),
            userId: excelUser.id,
            patientId: userDependent.id,
            specialtyId: specialty.id,
            city: appointment.city,
            state: appointment.state,
            status: 'approved',
          });

          if (appointment.observation) {
            await Observations.create({
              secure_id: uuid(),
              observation: appointment.observation,
              schedule_id: schedule.id,
            });
          }

          await Appointment.create({
            date: DateTime.fromFormat(appointment.openingDate, 'dd/MM/yyyy'),
            secureId: uuid(),
            userId: excelUser.id,
            partnerId: excelPartner.id,
            patientId: userDependent.id,
            specialtyId: specialty.id,
            scheduleId: schedule.id,
            partnerType: 'doctor',
            status: 'realized',
          });
        } else {
          if (userInfoOwner) {
            userOwner = await User.findBy('id', userInfoOwner.userId);
          } else {
            userOwner = await User.create({
              secureId: uuid(),
              email: `${appointment.ownerName.replace(/\s+/g, '').toLowerCase()}@noemail.com`,
              password: 'default_password',
              type: 'patient',
              is_active: true,
              deleted: false,
            });

            // Associa role PATIENT ao titular
            await userOwner.related('roles').attach([patientRole.id]);

            await UserInfo.create({
              userId: userOwner.id,
              name: appointment.ownerName,
              legalDocumentNumber: appointment.ownerCPF,
              typeDocument: 'cpf',
              dddCell: Number(appointment.patientPhone.slice(0, 2)),
              cell: Number(appointment.patientPhone.slice(2, 11)),
              city: appointment.city,
              state: appointment.state,
            });
          }

          if (userOwner && specialty) {
            const appointmentDate = DateTime.fromFormat(appointment.openingDate, 'dd/MM/yyyy');
            if (appointmentDate.isValid) {
              const existingAppointment = await Appointment.query()
                .where('patientId', userOwner.id)
                .where('specialtyId', specialty.id)
                .where('date', appointmentDate.toISODate()!)
                .first();

              if (existingAppointment) {
                this.logger.info(`[AVISO] Agendamento já existe para titular ${userOwner.id} em ${appointment.openingDate}. Pulando.`);
                continue;
              }
            }
          }

          const schedule = await Schedule.create({
            secureId: uuid(),
            userId: excelUser.id,
            patientId: userOwner.id,
            specialtyId: specialty.id,
            status: 'approved',
            city: appointment.city,
            state: appointment.state,
          });

          if (appointment.observation) {
            await Observations.create({
              secure_id: uuid(),
              observation: appointment.observation,
              schedule_id: schedule.id,
            });
          }

          await Appointment.create({
            date: DateTime.fromFormat(appointment.openingDate, 'dd/MM/yyyy'),
            secureId: uuid(),
            userId: excelUser.id,
            partnerId: excelPartner.id,
            patientId: userOwner.id,
            specialtyId: specialty.id,
            scheduleId: schedule.id,
            partnerType: 'doctor',
            status: 'realized',
          });
        }
      }

    } catch (error) {
      console.error('Erro ao importar dados: ', error);
    }
  }
}