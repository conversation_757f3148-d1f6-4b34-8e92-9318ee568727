import fs from 'fs'
import ExcelJS from 'exceljs'

import type User from 'App/Models/User'

interface ExcelRow {
  name: string
  email: string
  legalDocumentNumber: string
  password?: string
  holderLegalDocumentNumber?: string
  rowNumber: number
}

/**
 * Processa um job de importação de pacientes a partir de um arquivo Excel.
 * @param jobId O ID do ImportJob a ser processado.
 */
export default async function processPatientImport(jobId: number) {
  const ImportJob = (await import('App/Models/ImportJob')).default

  if (jobId === undefined) {
    console.warn('Recebido job com ID indefinido. Mensagem descartada.')
    return
  }

  const job = await ImportJob.find(jobId)

  if (!job) {
    console.warn(`Job de importação #${jobId} não encontrado. Mensagem descartada.`)
    return
  }

  if (!fs.existsSync(job.storedFilePath)) {
    console.warn(`Arquivo para o Job #${job.id} não encontrado em: ${job.storedFilePath}. Marcando como falho.`)
    job.status = 'failed'
    job.errorsReport = JSON.stringify({ error: 'Arquivo de importação não encontrado no servidor.' })
    await job.save()
    return
  }

  const allRowsData: ExcelRow[] = []
  try {
    const headerMap: { [key: string]: number } = {}
    const fileStream = fs.createReadStream(job.storedFilePath)
    const workbookReader = new ExcelJS.stream.xlsx.WorkbookReader(fileStream, {})

    for await (const worksheetReader of workbookReader) {
      for await (const row of worksheetReader) {
        if (row.number === 1) {
          if (row.values && Array.isArray(row.values)) {
            row.values.forEach((headerValue: any, index: number) => {
              if (headerValue) {
                headerMap[headerValue.toString()] = index;
              }
            });
          }
          continue
        }

        const getColumnValue = (name: string): any => {
          const colIndex = headerMap[name]
          return colIndex ? row.values[colIndex] : undefined
        }

        const currentRowData = {
          name: getColumnValue('Nome'),
          email: getColumnValue('E-mail'),
          legalDocumentNumber: getColumnValue('CPF'),
          password: getColumnValue('Senha (Não obrigatório)'),
          holderLegalDocumentNumber: getColumnValue('CPF titular (Não obrigatório)'),
          rowNumber: row.number,
        }

        if (currentRowData.name && currentRowData.email) {
          allRowsData.push({
            ...currentRowData,
            email: String(currentRowData.email).toLowerCase().trim(),
            legalDocumentNumber: String(currentRowData.legalDocumentNumber || '').replace(/\D/g, ''),
            holderLegalDocumentNumber: currentRowData.holderLegalDocumentNumber
              ? String(currentRowData.holderLegalDocumentNumber).replace(/\D/g, '')
              : undefined,
          })
        }
      }
    }
  } catch (readError) {
    console.error(`Falha ao ler o arquivo Excel para o Job #${job.id}`, readError)
    job.status = 'failed'
    job.errorsReport = JSON.stringify({
      error: 'Falha ao ler o arquivo Excel.',
      details: readError.message,
    })
    await job.save()
    return
  }

  job.status = 'processing'
  job.totalRows = allRowsData.length
  await job.save()
  console.info(`Processando Job #${job.id} com ${job.totalRows} linhas.`)

  const Database = (await import('@ioc:Adonis/Lucid/Database')).default
  const trx = await Database.transaction()

  const errorsReport: { rowNumber: number; data: any; error: string }[] = []

  try {
    const User = (await import('App/Models/User')).default
    const Role = (await import('App/Models/Role')).default

    const holderUsers = allRowsData.filter((user) => !user.holderLegalDocumentNumber)
    const dependentUsers = allRowsData.filter((user) => !!user.holderLegalDocumentNumber)

    const processedHolders: User[] = []
    const processedDependents: User[] = []
    const patientRole = await Role.query({ client: trx }).where('slug', 'patient').firstOrFail()

    let processedCount = 0
    let successCount = 0

    for (const currentUser of holderUsers) {
      try {
        const { email, password, rowNumber, holderLegalDocumentNumber, ...dataUserInfo } = currentUser

        if (!dataUserInfo.name || !email) {
          throw new Error('Nome e E-mail são obrigatórios.')
        }
        if (!dataUserInfo.legalDocumentNumber || dataUserInfo.legalDocumentNumber.length !== 11) {
          throw new Error('CPF está inválido ou vazio.')
        }

        let patient = await User.query({ client: trx }).where('email', email).preload('userInfo').first()

        if (patient) {
          patient.merge({ password: password || dataUserInfo.legalDocumentNumber, is_active: true })
          await patient.useTransaction(trx).save()
          await patient.related('userInfo').updateOrCreate({}, dataUserInfo)
        } else {
          patient = await User.create({ email, password: password || dataUserInfo.legalDocumentNumber, is_active: true, type: 'patient' }, { client: trx })
          await patient.related('userInfo').create({ typeDocument: 'cpf', ...dataUserInfo }, { client: trx })
          await patient.related('roles').attach([patientRole.id], trx)
        }

        await patient.load('userInfo')
        processedHolders.push(patient)
        successCount++
      } catch (rowError) {
        errorsReport.push({
          rowNumber: currentUser.rowNumber,
          data: currentUser,
          error: rowError.message,
        })
      }

      processedCount++
      if (processedCount % 10 === 0 || processedCount === allRowsData.length) {
        await ImportJob.query().where('id', jobId).update({ processedRows: processedCount })
      }
    }

    const holdersByCPF = new Map(
      processedHolders.map(p => [p.userInfo.legalDocumentNumber, p])
    );

    for (const dependent of dependentUsers) {
      try {
        const { email, password, rowNumber, holderLegalDocumentNumber, ...dependentUserData } = dependent
        const parentDocument = holderLegalDocumentNumber

        const parentData = holdersByCPF.get(parentDocument || '')
        if (!parentData) {
          console.warn(`Titular com CPF ${parentDocument} não encontrado para dependente da linha ${rowNumber}. Pulando.`)
          continue
        }

        let patientDependent = await User.query({ client: trx }).where('email', email).preload('userInfo').first()

        if (patientDependent) {
          patientDependent.merge({ password: password || dependentUserData.legalDocumentNumber, is_active: true, parentId: parentData.id, type: 'dependent' })
          await patientDependent.useTransaction(trx).save()
          await patientDependent.related('userInfo').updateOrCreate({}, dependentUserData)
        } else {
          patientDependent = await User.create({ email, password: password || dependentUserData.legalDocumentNumber, is_active: true, parentId: parentData.id, type: 'dependent' }, { client: trx })
          await patientDependent.related('userInfo').create({ typeDocument: 'cpf', ...dependentUserData }, { client: trx })
          await patientDependent.related('roles').attach([patientRole.id], trx)
        }
        processedDependents.push(patientDependent)
        successCount++
      } catch (rowError) {
        errorsReport.push({
          rowNumber: dependent.rowNumber,
          data: dependent,
          error: rowError.message,
        })
      }

      processedCount++
      if (processedCount % 10 === 0 || processedCount === allRowsData.length) {
        await ImportJob.query().where('id', jobId).update({ processedRows: processedCount })
      }
    }

    const partner = await User.findOrFail(job.partnerId, { client: trx })
    const allPatientIdsToAttach = [
      ...processedHolders.map((p) => p.id),
      ...processedDependents.map((d) => d.id),
    ]

    if (allPatientIdsToAttach.length > 0) {
      await partner.related('patients').attach(allPatientIdsToAttach, trx)
    }

    await trx.commit()
    console.info(`Transação do Job #${job.id} concluída.`)

    const finalJob = await ImportJob.find(jobId)
    if (finalJob) {
      finalJob.status = errorsReport.length > 0 ? 'failed_with_errors' : 'completed'
      finalJob.processedRows = successCount
      finalJob.errorsReport = errorsReport.length > 0 ? JSON.stringify(errorsReport) : null
      await finalJob.save()
      console.info(`Job de Importação #${job.id} finalizado com sucesso!`)
    }
  } catch (err) {
    await trx.rollback()
    console.error(`Falha crítica no Job de Importação #${job.id}, transação revertida.`, err)

    const jobToUpdate = await ImportJob.find(jobId)
    if (jobToUpdate) {
      jobToUpdate.status = 'failed'
      jobToUpdate.errorsReport = JSON.stringify({
        error: err.message,
        stack: err.stack,
      })
      await jobToUpdate.save()
    }
  } finally {
    if (job.storedFilePath && fs.existsSync(job.storedFilePath)) {
      await fs.promises.unlink(job.storedFilePath).catch((e) => console.error(`Falha ao deletar arquivo`, e))
    }
  }
}